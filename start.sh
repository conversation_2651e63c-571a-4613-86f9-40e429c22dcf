#!/bin/bash

# 中小型企业智能应用系统 - 一键启动脚本
# 作者: ZHT开发团队

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查 Node.js (通过 nvm)
    if [ -f ~/.nvm/nvm.sh ]; then
        source ~/.nvm/nvm.sh
        if ! command -v node &> /dev/null; then
            log_warning "Node.js 未安装，将尝试安装..."
            nvm install --lts
            nvm use --lts
        fi
    else
        log_warning "NVM 未安装，请确保 Node.js 已安装"
    fi
    
    log_success "依赖检查完成"
}

# 启动后端服务
start_backend() {
    log_step "启动后端服务 (PostgreSQL + Redis + Ollama + FastAPI)..."
    
    # 启动基础服务
    log_info "启动数据库和缓存服务..."
    docker-compose up -d postgres redis ollama
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 启动后端API
    log_info "启动 FastAPI 后端..."
    docker-compose up -d backend
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    sleep 15
    
    # 检查后端健康状态
    log_info "检查后端服务状态..."
    for i in {1..10}; do
        if curl -s http://localhost:8000/health > /dev/null; then
            log_success "后端服务启动成功！"
            break
        else
            if [ $i -eq 10 ]; then
                log_error "后端服务启动失败，请检查日志"
                docker-compose logs backend
                exit 1
            fi
            log_info "等待后端服务启动... ($i/10)"
            sleep 3
        fi
    done
}

# 启动前端服务
start_frontend() {
    log_step "启动前端服务 (React + TypeScript + Vite)..."
    
    cd frontend
    
    # 确保使用正确的 Node.js 版本
    if [ -f ~/.nvm/nvm.sh ]; then
        source ~/.nvm/nvm.sh
        nvm use --lts
    fi
    
    # 检查依赖是否已安装
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    # 启动前端开发服务器
    log_info "启动前端开发服务器..."
    nohup npm run dev > ../logs/frontend.log 2>&1 &
    echo $! > ../logs/frontend.pid
    
    cd ..
    
    # 等待前端启动
    log_info "等待前端服务启动..."
    sleep 10
    
    # 检查前端服务状态
    for i in {1..10}; do
        if curl -s http://localhost:5173 > /dev/null; then
            log_success "前端服务启动成功！"
            break
        else
            if [ $i -eq 10 ]; then
                log_error "前端服务启动失败，请检查日志"
                cat logs/frontend.log
                exit 1
            fi
            log_info "等待前端服务启动... ($i/10)"
            sleep 3
        fi
    done
}

# 显示服务状态
show_status() {
    log_step "显示服务状态..."
    
    echo -e "\n${CYAN}=== 服务状态 ===${NC}"
    docker-compose ps
    
    echo -e "\n${CYAN}=== 访问地址 ===${NC}"
    echo -e "🌐 前端应用:     ${GREEN}http://localhost:5173${NC}"
    echo -e "🔧 后端API:      ${GREEN}http://localhost:8000${NC}"
    echo -e "📚 API文档:      ${GREEN}http://localhost:8000/docs${NC}"
    echo -e "❤️  健康检查:    ${GREEN}http://localhost:8000/health${NC}"
    echo -e "🗄️  PostgreSQL:  ${GREEN}localhost:5432${NC}"
    echo -e "🔴 Redis:        ${GREEN}localhost:6379${NC}"
    echo -e "🤖 Ollama:       ${GREEN}localhost:11434${NC}"
    
    echo -e "\n${CYAN}=== 日志文件 ===${NC}"
    echo -e "📝 前端日志:     ${YELLOW}logs/frontend.log${NC}"
    echo -e "📝 后端日志:     ${YELLOW}docker-compose logs backend${NC}"
}

# 主函数
main() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "    中小型企业智能应用系统 - 一键启动脚本"
    echo "=================================================="
    echo -e "${NC}"
    
    # 创建日志目录
    mkdir -p logs
    
    # 检查依赖
    check_dependencies
    
    # 启动后端服务
    start_backend
    
    # 启动前端服务
    start_frontend
    
    # 显示服务状态
    show_status
    
    echo -e "\n${GREEN}🎉 所有服务启动完成！${NC}"
    echo -e "${YELLOW}💡 提示: 使用 ./stop.sh 停止所有服务${NC}"
    echo -e "${YELLOW}💡 提示: 使用 ./status.sh 查看服务状态${NC}"
    echo -e "${YELLOW}💡 提示: 使用 ./logs.sh 查看服务日志${NC}"
}

# 执行主函数
main "$@"
