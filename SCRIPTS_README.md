# 中小型企业智能应用系统 - 管理脚本

本项目提供了一套完整的管理脚本，用于简化开发环境的启动、停止、监控和维护。

## 📋 脚本列表

| 脚本名 | 功能 | 说明 |
|--------|------|------|
| `start.sh` | 一键启动 | 启动所有服务（后端+前端） |
| `stop.sh` | 一键停止 | 停止所有服务并清理资源 |
| `restart.sh` | 重启服务 | 重启指定或全部服务 |
| `status.sh` | 状态检查 | 查看所有服务运行状态 |
| `logs.sh` | 日志查看 | 查看各服务的日志信息 |

## 🚀 快速开始

### 启动系统
```bash
# 一键启动所有服务
./start.sh
```

### 停止系统
```bash
# 一键停止所有服务
./stop.sh

# 停止服务并清理Docker资源
./stop.sh --clean

# 停止服务并清理日志文件
./stop.sh --clean-logs
```

### 查看状态
```bash
# 查看基本状态
./status.sh

# 查看详细状态信息
./status.sh --detailed
```

## 📖 详细使用说明

### 1. start.sh - 启动脚本

**功能**: 一键启动整个系统的所有服务

**执行流程**:
1. 检查系统依赖（Docker、Node.js等）
2. 启动后端服务（PostgreSQL、Redis、Ollama、FastAPI）
3. 启动前端服务（React + Vite）
4. 验证服务状态
5. 显示访问地址

**使用方法**:
```bash
./start.sh
```

**输出示例**:
```
==================================================
    中小型企业智能应用系统 - 一键启动脚本
==================================================
[STEP] 检查系统依赖...
[STEP] 启动后端服务...
[STEP] 启动前端服务...
🎉 所有服务启动完成！
```

### 2. stop.sh - 停止脚本

**功能**: 停止所有服务并可选择性清理资源

**使用方法**:
```bash
# 基本停止
./stop.sh

# 停止并清理Docker资源
./stop.sh --clean

# 停止并清理日志文件
./stop.sh --clean-logs

# 停止并清理所有资源
./stop.sh --clean --clean-logs
```

**选项说明**:
- `--clean`: 清理未使用的Docker镜像和容器
- `--clean-logs`: 清理所有日志文件
- `--help`: 显示帮助信息

### 3. status.sh - 状态检查脚本

**功能**: 检查所有服务的运行状态和系统资源使用情况

**使用方法**:
```bash
# 基本状态检查
./status.sh

# 详细状态信息
./status.sh --detailed
```

**检查内容**:
- 各服务的HTTP状态
- 端口占用情况
- Docker容器状态
- 系统资源使用
- 网络端口状态

### 4. logs.sh - 日志查看脚本

**功能**: 查看各个服务的日志信息

**使用方法**:
```bash
# 查看后端日志（最后50行）
./logs.sh backend

# 实时跟踪前端日志
./logs.sh frontend -f

# 查看最后100行日志
./logs.sh backend -n 100

# 查看所有服务日志
./logs.sh all

# 清空前端日志
./logs.sh frontend --clear
```

**支持的服务**:
- `frontend`: 前端服务日志
- `backend`: 后端API日志
- `postgres`: PostgreSQL数据库日志
- `redis`: Redis缓存日志
- `ollama`: Ollama AI服务日志
- `all`: 所有服务日志

**选项说明**:
- `-f, --follow`: 实时跟踪日志
- `-n, --lines`: 指定显示行数
- `--clear`: 清空指定服务日志

### 5. restart.sh - 重启脚本

**功能**: 重启指定服务或全部服务

**使用方法**:
```bash
# 重启所有服务
./restart.sh

# 重启后端服务
./restart.sh backend

# 快速重启前端（不重新安装依赖）
./restart.sh frontend --quick

# 重新构建并重启
./restart.sh all --build

# 清理后重启
./restart.sh all --clean
```

**支持的服务**:
- `frontend`: 前端服务
- `backend`: 后端API服务
- `postgres`: PostgreSQL数据库
- `redis`: Redis缓存
- `ollama`: Ollama AI服务
- `all`: 所有服务（默认）

**选项说明**:
- `--quick`: 快速重启（跳过依赖安装）
- `--build`: 重新构建Docker镜像
- `--clean`: 清理后重启

## 🔧 系统要求

### 必需依赖
- **Docker**: 用于运行后端服务
- **Docker Compose**: 用于编排多个容器
- **Node.js**: 用于运行前端服务（推荐通过nvm安装）

### 推荐工具
- **NVM**: Node.js版本管理工具
- **curl**: 用于健康检查
- **lsof**: 用于端口检查

## 📁 目录结构

```
ZHT_SYS/
├── start.sh          # 启动脚本
├── stop.sh           # 停止脚本
├── restart.sh        # 重启脚本
├── status.sh         # 状态检查脚本
├── logs.sh           # 日志查看脚本
├── logs/             # 日志目录
│   ├── frontend.log  # 前端日志
│   └── frontend.pid  # 前端进程ID
├── docker-compose.yml
├── backend/
├── frontend/
└── docs/
```

## 🌐 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| 前端 | 5173 | React开发服务器 |
| 后端 | 8000 | FastAPI服务 |
| PostgreSQL | 5432 | 数据库服务 |
| Redis | 6379 | 缓存服务 |
| Ollama | 11434 | AI模型服务 |

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :5173
   
   # 强制释放端口
   sudo lsof -ti:5173 | xargs kill -9
   ```

2. **Docker服务启动失败**
   ```bash
   # 查看Docker日志
   ./logs.sh backend
   
   # 重新构建镜像
   ./restart.sh backend --build
   ```

3. **前端服务无法启动**
   ```bash
   # 检查Node.js版本
   node --version
   
   # 重新安装依赖
   cd frontend && npm install
   ```

4. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x *.sh
   ```

### 日志位置

- **前端日志**: `logs/frontend.log`
- **后端日志**: `docker-compose logs backend`
- **数据库日志**: `docker-compose logs postgres`

## 💡 使用技巧

1. **开发工作流**:
   ```bash
   ./start.sh          # 启动开发环境
   ./status.sh         # 检查状态
   # 进行开发工作...
   ./logs.sh frontend -f  # 实时查看前端日志
   ./stop.sh           # 结束工作时停止服务
   ```

2. **快速重启前端**（代码修改后）:
   ```bash
   ./restart.sh frontend --quick
   ```

3. **完全重置环境**:
   ```bash
   ./stop.sh --clean --clean-logs
   ./start.sh
   ```

4. **监控服务状态**:
   ```bash
   watch -n 5 ./status.sh  # 每5秒刷新状态
   ```

## 📞 技术支持

如果遇到问题，请按以下步骤排查：

1. 运行 `./status.sh --detailed` 查看详细状态
2. 查看相关服务日志 `./logs.sh [服务名]`
3. 尝试重启服务 `./restart.sh [服务名]`
4. 如果问题持续，请提供状态和日志信息

---

**作者**: ZHT开发团队  
**版本**: 1.0.0  
**更新时间**: 2024年
