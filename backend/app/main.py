"""
中小型企业智能应用系统 - 主应用入口
FastAPI 应用程序的主要配置和路由设置

作者: ZHT开发团队
创建时间: 2024
"""

from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import time
import logging

from app.core.config import settings
from app.core.database import engine, Base
from app.api.api_v1.api import api_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="中小型企业智能应用开发架构 - 集成AI能力的企业级应用平台",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# CORS中间件配置 - 允许前端跨域访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 可信主机中间件 - 防止Host头攻击
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)


# 请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """
    添加请求处理时间到响应头
    用于性能监控和调试
    """
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# 全局异常处理器
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """
    处理HTTP异常，返回统一格式的错误响应
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "path": str(request.url)
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    处理请求验证错误，返回详细的验证错误信息
    """
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": True,
            "message": "请求数据验证失败",
            "details": exc.errors(),
            "status_code": 422,
            "path": str(request.url)
        }
    )


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    """
    应用启动时执行的初始化操作
    """
    logger.info("🚀 启动中小型企业智能应用系统...")

    # 创建数据库表 (开发环境)
    if settings.ENVIRONMENT == "development":
        logger.info("📊 初始化数据库表...")
        # 注意: 生产环境应使用Alembic进行数据库迁移
        # Base.metadata.create_all(bind=engine)

    logger.info("✅ 系统启动完成!")


# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """
    应用关闭时执行的清理操作
    """
    logger.info("🛑 正在关闭系统...")
    logger.info("✅ 系统已安全关闭")


# 健康检查端点
@app.get("/health", tags=["系统监控"])
async def health_check():
    """
    系统健康检查端点
    用于负载均衡器和监控系统检查服务状态
    """
    return {
        "status": "healthy",
        "message": "系统运行正常",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


# 根路径
@app.get("/", tags=["系统信息"])
async def root():
    """
    根路径，返回系统基本信息
    """
    return {
        "message": "欢迎使用中小型企业智能应用系统",
        "version": "1.0.0",
        "docs": "/docs",
        "api": f"{settings.API_V1_STR}",
        "environment": settings.ENVIRONMENT
    }


# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


if __name__ == "__main__":
    import uvicorn

    # 开发环境直接运行
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
