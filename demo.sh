#!/bin/bash

# 中小型企业智能应用系统 - 演示脚本
# 展示管理脚本的使用方法

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}"
echo "=================================================="
echo "    中小型企业智能应用系统 - 管理脚本演示"
echo "=================================================="
echo -e "${NC}"

echo -e "${YELLOW}本演示将展示如何使用项目提供的管理脚本${NC}\n"

# 显示可用脚本
echo -e "${PURPLE}📋 可用的管理脚本:${NC}"
echo -e "  ${GREEN}./start.sh${NC}    - 一键启动所有服务"
echo -e "  ${GREEN}./stop.sh${NC}     - 一键停止所有服务"
echo -e "  ${GREEN}./restart.sh${NC}  - 重启指定或全部服务"
echo -e "  ${GREEN}./status.sh${NC}   - 查看服务运行状态"
echo -e "  ${GREEN}./logs.sh${NC}     - 查看服务日志"

echo -e "\n${PURPLE}🌐 当前服务访问地址:${NC}"
echo -e "  前端应用: ${BLUE}http://localhost:5173${NC}"
echo -e "  后端API:  ${BLUE}http://localhost:8000${NC}"
echo -e "  API文档:  ${BLUE}http://localhost:8000/docs${NC}"

echo -e "\n${PURPLE}💡 常用命令示例:${NC}"
echo -e "  ${YELLOW}# 查看当前状态${NC}"
echo -e "  ./status.sh"
echo -e ""
echo -e "  ${YELLOW}# 查看后端日志${NC}"
echo -e "  ./logs.sh backend"
echo -e ""
echo -e "  ${YELLOW}# 重启前端服务${NC}"
echo -e "  ./restart.sh frontend"
echo -e ""
echo -e "  ${YELLOW}# 停止所有服务${NC}"
echo -e "  ./stop.sh"
echo -e ""
echo -e "  ${YELLOW}# 重新启动所有服务${NC}"
echo -e "  ./start.sh"

echo -e "\n${GREEN}✅ 系统已准备就绪，您可以开始使用管理脚本了！${NC}"
echo -e "${YELLOW}💡 提示: 查看 SCRIPTS_README.md 获取详细使用说明${NC}"
