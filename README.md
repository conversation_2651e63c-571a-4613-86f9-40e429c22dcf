# 中小型企业智能应用开发架构 (ZHT_SYS)

## 项目概述

本项目是一套面向中小型企业的智能应用开发架构，集成了现代化的前后端技术栈、AI能力和云原生部署方案。该架构旨在帮助企业快速构建具备智能问答、文档处理和知识管理能力的应用系统。

## 技术栈

### 前端技术栈
- **React 18+** - 现代化前端框架
- **TypeScript 5.0+** - 类型安全的JavaScript超集
- **Tailwind CSS 3.0+** - 实用优先的CSS框架
- **React Query** - 服务器状态管理
- **Zustand** - 客户端状态管理
- **Shadcn UI** - 现代化组件库

### 后端技术栈
- **FastAPI** - 高性能Python Web框架
- **PostgreSQL 15+** - 关系型数据库
- **pgvector** - 向量数据库扩展
- **SQLAlchemy 2.0+** - Python ORM
- **Alembic** - 数据库迁移工具

### 中间件与存储
- **Redis 7.0+** - 缓存和消息队列
- **Celery 5.0+** - 异步任务处理
- **阿里云OSS** - 对象存储服务

### AI模型集成
- **开发环境**: Ollama (llama3.2, qwen2)
- **生产环境**: 阿里云百炼
- **RAG框架**: morphik-core
- **向量嵌入**: nomic-embed-text / 阿里云嵌入模型

### 部署方案
- **开发环境**: Docker Compose
- **生产环境**: 阿里云容器服务ACK (Kubernetes)
- **CI/CD**: GitHub Actions

## 项目结构

```
ZHT_SYS/
├── docs/                    # 文档目录
│   ├── architecture/        # 架构设计文档
│   ├── deployment/          # 部署指南
│   ├── development/         # 开发指南
│   └── troubleshooting/     # 故障排查
├── frontend/                # 前端应用
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/                 # 后端API
│   ├── app/
│   ├── alembic/
│   ├── requirements.txt
│   └── Dockerfile
├── infrastructure/          # 基础设施配置
│   ├── docker/
│   ├── kubernetes/
│   └── terraform/
├── scripts/                 # 自动化脚本
└── docker-compose.yml       # 本地开发环境
```

## 快速开始

### 前置要求
- Docker & Docker Compose
- Node.js 18+
- Python 3.10+
- Git

### 本地开发环境搭建

1. **克隆项目**
```bash
git clone <repository-url>
cd ZHT_SYS
```

2. **启动开发环境**
```bash
# 启动所有服务
docker-compose up -d

# 安装前端依赖
cd frontend && npm install

# 安装后端依赖
cd backend && pip install -r requirements.txt
```

3. **初始化数据库**
```bash
cd backend
alembic upgrade head
```

4. **启动开发服务器**
```bash
# 启动后端 (终端1)
cd backend && uvicorn app.main:app --reload

# 启动前端 (终端2)
cd frontend && npm run dev
```

## 核心功能

### 1. 用户管理系统
- 用户注册与登录
- 角色权限管理
- 多租户支持
- JWT认证

### 2. 文档处理系统
- 文档上传与验证
- 多格式文档解析 (PDF, Word, TXT等)
- 文档分块与向量化
- 文档版本管理

### 3. RAG知识库
- 基于morphik-core的知识库构建
- 智能检索与排序
- 上下文感知的问答
- 知识库管理界面

### 4. AI模型集成
- 多模型支持 (本地/云端)
- 模型切换与配置
- 性能监控与优化
- 成本控制

## 快速开始 (一键启动)

我们提供了完整的管理脚本来简化开发环境的管理：

```bash
# 一键启动所有服务
./start.sh

# 查看服务状态
./status.sh

# 查看服务日志
./logs.sh backend

# 重启指定服务
./restart.sh frontend

# 一键停止所有服务
./stop.sh
```

### 📋 管理脚本说明

| 脚本 | 功能 | 使用示例 |
|------|------|----------|
| `start.sh` | 一键启动所有服务 | `./start.sh` |
| `stop.sh` | 一键停止所有服务 | `./stop.sh --clean` |
| `restart.sh` | 重启指定或全部服务 | `./restart.sh backend` |
| `status.sh` | 查看服务运行状态 | `./status.sh --detailed` |
| `logs.sh` | 查看服务日志 | `./logs.sh frontend -f` |

**详细使用说明**: 请查看 [SCRIPTS_README.md](./SCRIPTS_README.md)

## 文档导航

### 📚 核心文档
- [系统架构设计](docs/architecture/system-architecture.md) - 了解系统整体设计
- [技术栈介绍](docs/development/technology-guide.md) - 面向初学者的技术介绍
- [开发环境搭建](docs/development/setup-guide.md) - 详细的环境配置指南
- [部署指南](docs/deployment/production-deployment.md) - 生产环境部署方案

### 🔧 开发指南
- [API接口文档](docs/development/api-reference.md) - RESTful API详细说明
- [前端开发指南](docs/development/frontend-guide.md) - React + TypeScript开发
- [后端开发指南](docs/development/backend-guide.md) - FastAPI + PostgreSQL开发
- [AI集成指南](docs/development/ai-integration.md) - AI模型集成方案

### 🚀 运维指南
- [Docker部署](docs/deployment/docker-deployment.md) - 容器化部署方案
- [Kubernetes部署](docs/deployment/k8s-deployment.md) - 云原生部署
- [监控和日志](docs/deployment/monitoring.md) - 系统监控方案
- [故障排查](docs/troubleshooting/common-issues.md) - 常见问题解决

### 📖 学习资源
- [技术学习路径](docs/development/learning-path.md) - 从入门到精通
- [最佳实践](docs/development/best-practices.md) - 开发规范和建议
- [代码示例](docs/examples/) - 完整的功能示例
- [FAQ常见问题](docs/faq.md) - 常见问题解答

## 项目特色

### 🎯 面向初学者
- **详细的中文文档**: 每个技术点都有详细说明
- **完整的示例代码**: 包含注释和最佳实践
- **渐进式学习**: 从基础到高级的学习路径
- **故障排查指南**: 常见问题的解决方案

### 🏗️ 企业级架构
- **微服务设计**: 模块化、可扩展的架构
- **云原生支持**: 支持Docker和Kubernetes部署
- **高可用性**: 负载均衡、故障转移、自动恢复
- **安全性**: 认证授权、数据加密、安全审计

### 🤖 AI能力集成
- **本地AI模型**: 开发环境使用Ollama
- **云端AI服务**: 生产环境使用阿里云百炼
- **RAG知识库**: 基于morphik-core的智能问答
- **向量检索**: pgvector支持的语义搜索

### 📊 完整的监控
- **应用监控**: 性能指标、错误追踪
- **基础设施监控**: 服务器、数据库、缓存
- **业务监控**: 用户行为、业务指标
- **日志聚合**: 结构化日志、实时分析

## 技术支持

### 🆘 获取帮助
- **GitHub Issues**: [提交问题](https://github.com/your-repo/issues)
- **技术文档**: 查阅详细的技术文档
- **社区讨论**: 参与技术讨论和交流
- **专业支持**: 联系技术团队获取专业支持

### 📞 联系方式
- **邮箱**: <EMAIL>
- **微信群**: 扫码加入技术交流群
- **QQ群**: 123456789
- **官网**: https://your-domain.com

## 贡献指南

我们欢迎所有形式的贡献！

### 🤝 如何贡献
1. **Fork项目** - 创建你的功能分支
2. **提交代码** - 遵循代码规范
3. **创建PR** - 详细描述你的改动
4. **代码审查** - 等待维护者审查

### 📝 贡献类型
- **代码贡献**: 新功能、Bug修复、性能优化
- **文档贡献**: 完善文档、翻译、示例
- **测试贡献**: 编写测试用例、发现Bug
- **设计贡献**: UI/UX设计、图标、插画

详细信息请参阅 [贡献指南](docs/development/contributing.md)

## 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

## 致谢

感谢所有为这个项目做出贡献的开发者和社区成员！

### 🙏 特别感谢
- **开源社区**: 提供了优秀的开源工具和库
- **技术专家**: 提供了宝贵的技术指导
- **测试用户**: 帮助发现和修复问题
- **文档贡献者**: 完善了项目文档

---

**💡 提示**: 本项目专为中小型企业和技术基础较弱的开发团队设计，提供了完整的学习路径和详细的中文文档。无论你是初学者还是有经验的开发者，都能在这里找到有价值的内容。
