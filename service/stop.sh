#!/bin/bash

# 中小型企业智能应用系统 - 一键停止脚本
# 作者: ZHT开发团队

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 停止前端服务
stop_frontend() {
    log_step "停止前端服务..."
    
    # 检查前端进程是否存在
    if [ -f "logs/frontend.pid" ]; then
        local pid=$(cat logs/frontend.pid)
        if ps -p $pid > /dev/null 2>&1; then
            log_info "停止前端进程 (PID: $pid)..."
            kill $pid
            sleep 2
            
            # 强制杀死进程（如果还在运行）
            if ps -p $pid > /dev/null 2>&1; then
                log_warning "强制停止前端进程..."
                kill -9 $pid
            fi
        else
            log_info "前端进程已停止"
        fi
        rm -f logs/frontend.pid
    else
        log_info "未找到前端进程文件"
    fi
    
    # 查找并停止所有可能的前端进程
    log_info "查找并停止所有前端相关进程..."
    pkill -f "vite" || true
    pkill -f "npm run dev" || true
    
    log_success "前端服务已停止"
}

# 停止后端服务
stop_backend() {
    log_step "停止后端服务..."
    
    # 停止 Docker 容器
    log_info "停止 Docker 容器..."
    docker-compose down
    
    log_success "后端服务已停止"
}

# 清理资源
cleanup() {
    log_step "清理系统资源..."
    
    # 清理未使用的 Docker 资源（可选）
    if [ "$1" = "--clean" ]; then
        log_info "清理 Docker 资源..."
        docker system prune -f
        docker volume prune -f
        log_success "Docker 资源清理完成"
    fi
    
    # 清理日志文件（可选）
    if [ "$1" = "--clean-logs" ] || [ "$2" = "--clean-logs" ]; then
        log_info "清理日志文件..."
        rm -rf logs/*.log
        log_success "日志文件清理完成"
    fi
}

# 显示停止状态
show_stop_status() {
    log_step "检查服务状态..."
    
    echo -e "\n${CYAN}=== Docker 容器状态 ===${NC}"
    docker-compose ps || true
    
    echo -e "\n${CYAN}=== 端口占用检查 ===${NC}"
    local ports=(5173 8000 5432 6379 11434)
    for port in "${ports[@]}"; do
        if lsof -i :$port > /dev/null 2>&1; then
            echo -e "端口 $port: ${RED}仍被占用${NC}"
        else
            echo -e "端口 $port: ${GREEN}已释放${NC}"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}用法: $0 [选项]${NC}"
    echo ""
    echo "选项:"
    echo "  --clean        停止服务后清理 Docker 资源"
    echo "  --clean-logs   停止服务后清理日志文件"
    echo "  --help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 停止所有服务"
    echo "  $0 --clean           # 停止服务并清理 Docker 资源"
    echo "  $0 --clean-logs      # 停止服务并清理日志"
    echo "  $0 --clean --clean-logs  # 停止服务并清理所有资源"
}

# 主函数
main() {
    # 检查帮助参数
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        show_help
        exit 0
    fi
    
    echo -e "${CYAN}"
    echo "=================================================="
    echo "    中小型企业智能应用系统 - 一键停止脚本"
    echo "=================================================="
    echo -e "${NC}"
    
    # 创建日志目录（如果不存在）
    mkdir -p logs
    
    # 停止前端服务
    stop_frontend
    
    # 停止后端服务
    stop_backend
    
    # 清理资源
    cleanup "$@"
    
    # 显示停止状态
    show_stop_status
    
    echo -e "\n${GREEN}🛑 所有服务已停止！${NC}"
    echo -e "${YELLOW}💡 提示: 使用 ./start.sh 重新启动所有服务${NC}"
    
    # 如果有端口仍被占用，提供解决建议
    local occupied_ports=()
    local ports=(5173 8000 5432 6379 11434)
    for port in "${ports[@]}"; do
        if lsof -i :$port > /dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo -e "\n${YELLOW}⚠️  警告: 以下端口仍被占用: ${occupied_ports[*]}${NC}"
        echo -e "${YELLOW}💡 如需强制释放，请运行: sudo lsof -ti:端口号 | xargs kill -9${NC}"
    fi
}

# 执行主函数
main "$@"
