#!/bin/bash

# 中小型企业智能应用系统 - 服务状态检查脚本
# 作者: ZHT开发团队

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查服务状态
check_service() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        echo -e "${GREEN}✅ $service_name${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name${NC}"
        return 1
    fi
}

# 检查端口占用
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -i :$port > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $service_name (端口 $port)${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name (端口 $port)${NC}"
        return 1
    fi
}

# 获取服务信息
get_service_info() {
    local url=$1
    local service_name=$2
    
    echo -e "\n${CYAN}=== $service_name 详细信息 ===${NC}"
    
    local response=$(curl -s "$url" 2>/dev/null)
    if [ $? -eq 0 ] && [ -n "$response" ]; then
        echo "$response" | python3 -m json.tool 2>/dev/null || echo "$response"
    else
        echo -e "${RED}无法获取服务信息${NC}"
    fi
}

# 主函数
main() {
    echo -e "${CYAN}"
    echo "=================================================="
    echo "    中小型企业智能应用系统 - 服务状态检查"
    echo "=================================================="
    echo -e "${NC}"
    
    echo -e "\n${PURPLE}[检查服务状态]${NC}"
    
    # 检查后端服务
    echo -e "\n${BLUE}后端服务:${NC}"
    check_service "FastAPI 后端" "http://localhost:8000/health"
    check_service "API 文档" "http://localhost:8000/docs"
    
    # 检查前端服务
    echo -e "\n${BLUE}前端服务:${NC}"
    check_service "React 前端" "http://localhost:5173"
    
    # 检查数据库和缓存
    echo -e "\n${BLUE}数据库和缓存:${NC}"
    check_port 5432 "PostgreSQL"
    check_port 6379 "Redis"
    
    # 检查AI服务
    echo -e "\n${BLUE}AI 服务:${NC}"
    check_port 11434 "Ollama"
    
    # Docker 容器状态
    echo -e "\n${PURPLE}[Docker 容器状态]${NC}"
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        echo -e "${RED}Docker Compose 未安装${NC}"
    fi
    
    # 系统资源使用情况
    echo -e "\n${PURPLE}[系统资源使用]${NC}"
    echo -e "${BLUE}内存使用:${NC}"
    free -h | head -2
    
    echo -e "\n${BLUE}磁盘使用:${NC}"
    df -h | grep -E "(Filesystem|/dev/)"
    
    # 网络端口状态
    echo -e "\n${PURPLE}[网络端口状态]${NC}"
    echo -e "${BLUE}应用端口占用情况:${NC}"
    local ports=(5173 8000 5432 6379 11434)
    for port in "${ports[@]}"; do
        local process=$(lsof -ti :$port 2>/dev/null)
        if [ -n "$process" ]; then
            local process_name=$(ps -p $process -o comm= 2>/dev/null || echo "未知")
            echo -e "端口 $port: ${GREEN}已占用${NC} (进程: $process_name, PID: $process)"
        else
            echo -e "端口 $port: ${RED}未占用${NC}"
        fi
    done
    
    # 获取详细服务信息
    if [ "$1" = "--detailed" ] || [ "$1" = "-d" ]; then
        get_service_info "http://localhost:8000/health" "后端健康检查"
        get_service_info "http://localhost:8000/" "后端根路径"
    fi
    
    # 日志文件状态
    echo -e "\n${PURPLE}[日志文件状态]${NC}"
    if [ -d "logs" ]; then
        echo -e "${BLUE}日志目录内容:${NC}"
        ls -la logs/ 2>/dev/null || echo "日志目录为空"
        
        if [ -f "logs/frontend.log" ]; then
            local log_size=$(du -h logs/frontend.log | cut -f1)
            echo -e "前端日志大小: ${YELLOW}$log_size${NC}"
        fi
    else
        echo -e "${YELLOW}日志目录不存在${NC}"
    fi
    
    # 快速健康检查总结
    echo -e "\n${PURPLE}[快速健康检查]${NC}"
    local all_healthy=true
    
    # 检查关键服务
    if ! curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo -e "${RED}❌ 后端服务异常${NC}"
        all_healthy=false
    fi
    
    if ! curl -s http://localhost:5173 > /dev/null 2>&1; then
        echo -e "${RED}❌ 前端服务异常${NC}"
        all_healthy=false
    fi
    
    if ! lsof -i :5432 > /dev/null 2>&1; then
        echo -e "${RED}❌ PostgreSQL 未运行${NC}"
        all_healthy=false
    fi
    
    if ! lsof -i :6379 > /dev/null 2>&1; then
        echo -e "${RED}❌ Redis 未运行${NC}"
        all_healthy=false
    fi
    
    if $all_healthy; then
        echo -e "${GREEN}🎉 所有核心服务运行正常！${NC}"
    else
        echo -e "${RED}⚠️  部分服务存在问题，请检查上述状态${NC}"
    fi
    
    # 访问地址提醒
    echo -e "\n${CYAN}=== 快速访问 ===${NC}"
    echo -e "🌐 前端应用:     ${GREEN}http://localhost:5173${NC}"
    echo -e "🔧 后端API:      ${GREEN}http://localhost:8000${NC}"
    echo -e "📚 API文档:      ${GREEN}http://localhost:8000/docs${NC}"
    echo -e "❤️  健康检查:    ${GREEN}http://localhost:8000/health${NC}"
    
    echo -e "\n${YELLOW}💡 提示: 使用 $0 --detailed 查看详细信息${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}用法: $0 [选项]${NC}"
    echo ""
    echo "选项:"
    echo "  --detailed, -d    显示详细的服务信息"
    echo "  --help, -h        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                # 显示基本状态信息"
    echo "  $0 --detailed     # 显示详细状态信息"
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

# 执行主函数
main "$@"
