pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Atelier Forest
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atelier-forest
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1b1918  Default Background
base01  #2c2421  Lighter Background (Used for status bars, line number and folding marks)
base02  #68615e  Selection Background
base03  #766e6b  Comments, Invisibles, Line Highlighting
base04  #9c9491  Dark Foreground (Used for status bars)
base05  #a8a19f  Default Foreground, Caret, Delimiters, Operators
base06  #e6e2e0  Light Foreground (Not often used)
base07  #f1efee  Light Background (Not often used)
base08  #f22c40  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #df5320  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #c38418  Classes, Markup Bold, Search Text Background
base0B  #7b9726  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #3d97b8  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #407ee7  Functions, Methods, Attribute IDs, Headings
base0E  #6666ea  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #c33ff3  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #a8a19f;
  background: #1b1918
}
.hljs::selection,
.hljs ::selection {
  background-color: #68615e;
  color: #a8a19f
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #766e6b -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #766e6b
}
/* base04 - #9c9491 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #9c9491
}
/* base05 - #a8a19f -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #a8a19f
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #f22c40
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #df5320
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #c38418
}
.hljs-strong {
  font-weight: bold;
  color: #c38418
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7b9726
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #3d97b8
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #407ee7
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #6666ea
}
.hljs-emphasis {
  color: #6666ea;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #c33ff3
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}