pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

 highlight.js style for MikroTik RouterOS script

*/
.hljs {
  color: #444;
  background: #F0F0F0
}
/* Base color: saturation 0; */
.hljs-subst {
  color: #444
}
.hljs-comment {
  color: #888888
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-meta .hljs-keyword,
.hljs-doctag,
.hljs-name {
  font-weight: bold
}
.hljs-attribute {
  color: #0E9A00
}
.hljs-function {
  color: #99069A
}
/* User color: hue: 0 */
.hljs-type,
.hljs-string,
.hljs-number,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion {
  color: #880000
}
.hljs-title,
.hljs-section {
  color: #880000;
  font-weight: bold
}
.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo {
  color: #BC6060
}
/* Language color: hue: 90; */
.hljs-literal {
  color: #78A960
}
.hljs-built_in,
.hljs-bullet,
.hljs-code,
.hljs-addition {
  color: #0C9A9A
}
/* Meta color: hue: 200 */
.hljs-meta {
  color: #1f7199
}
.hljs-meta .hljs-string {
  color: #4d99bf
}
/* Misc effects */
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}