pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

Lightfair style (c) <PERSON><PERSON><PERSON> <<EMAIL>>

*/
.hljs {
  color: #444;
  background: #fff
}
.hljs-name {
  color: #01a3a3
}
.hljs-tag,
.hljs-meta {
  color: #778899
}
.hljs-subst {
  /* default */
  
}
.hljs-comment {
  color: #888888
}
.hljs-keyword,
.hljs-attribute,
.hljs-selector-tag,
.hljs-meta .hljs-keyword,
.hljs-doctag,
.hljs-name {
  font-weight: bold
}
.hljs-type,
.hljs-string,
.hljs-number,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion {
  color: #4286f4
}
.hljs-title,
.hljs-section {
  color: #4286f4;
  font-weight: bold
}
.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo {
  color: #BC6060
}
.hljs-literal {
  color: #62bcbc
}
.hljs-built_in,
.hljs-bullet,
.hljs-code,
.hljs-addition {
  color: #25c6c6
}
.hljs-meta .hljs-string {
  color: #4d99bf
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}