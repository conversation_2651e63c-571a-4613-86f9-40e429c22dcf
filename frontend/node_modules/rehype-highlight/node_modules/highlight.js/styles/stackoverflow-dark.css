pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: StackOverflow Dark
  Description: Dark theme as used on stackoverflow.com
  Author: stackoverflow.com
  Maintainer: @Hirse
  Website: https://github.com/StackExchange/Stacks
  License: MIT
  Updated: 2021-05-15

  Updated for @stackoverflow/stacks v0.64.0
  Code Blocks: /blob/v0.64.0/lib/css/components/_stacks-code-blocks.less
  Colors: /blob/v0.64.0/lib/css/exports/_stacks-constants-colors.less
*/
.hljs {
  /* var(--highlight-color) */
  color: #ffffff;
  /* var(--highlight-bg) */
  background: #1c1b1b
}
.hljs-subst {
  /* var(--highlight-color) */
  color: #ffffff
}
.hljs-comment {
  /* var(--highlight-comment) */
  color: #999999
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-meta .hljs-keyword,
.hljs-doctag,
.hljs-section {
  /* var(--highlight-keyword) */
  color: #88aece
}
.hljs-attr {
  /* var(--highlight-attribute); */
  color: #88aece
}
.hljs-attribute {
  /* var(--highlight-symbol) */
  color: #c59bc1
}
.hljs-name,
.hljs-type,
.hljs-number,
.hljs-selector-id,
.hljs-quote,
.hljs-template-tag {
  /* var(--highlight-namespace) */
  color: #f08d49
}
.hljs-selector-class {
  /* var(--highlight-keyword) */
  color: #88aece
}
.hljs-string,
.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr {
  /* var(--highlight-variable) */
  color: #b5bd68
}
.hljs-meta,
.hljs-selector-pseudo {
  /* var(--highlight-keyword) */
  color: #88aece
}
.hljs-built_in,
.hljs-title,
.hljs-literal {
  /* var(--highlight-literal) */
  color: #f08d49
}
.hljs-bullet,
.hljs-code {
  /* var(--highlight-punctuation) */
  color: #cccccc
}
.hljs-meta .hljs-string {
  /* var(--highlight-variable) */
  color: #b5bd68
}
.hljs-deletion {
  /* var(--highlight-deletion) */
  color: #de7176
}
.hljs-addition {
  /* var(--highlight-addition) */
  color: #76c490
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}
.hljs-formula,
.hljs-operator,
.hljs-params,
.hljs-property,
.hljs-punctuation,
.hljs-tag {
  /* purposely ignored */
  
}