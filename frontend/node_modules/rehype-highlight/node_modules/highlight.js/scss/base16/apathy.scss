pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Apathy
  Author: <PERSON><PERSON> (https://github.com/janniks)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme apathy
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #031A16  Default Background
base01  #0B342D  Lighter Background (Used for status bars, line number and folding marks)
base02  #184E45  Selection Background
base03  #2B685E  Comments, Invisibles, Line Highlighting
base04  #5F9C92  Dark Foreground (Used for status bars)
base05  #81B5AC  Default Foreground, Caret, Delimiters, Operators
base06  #A7CEC8  Light Foreground (Not often used)
base07  #D2E7E4  Light Background (Not often used)
base08  #3E9688  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #3E7996  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #3E4C96  Classes, Markup Bold, Search Text Background
base0B  #883E96  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #963E4C  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #96883E  Functions, Methods, Attribute IDs, Headings
base0E  #4C963E  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #3E965B  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #81B5AC;
  background: #031A16
}
.hljs::selection,
.hljs ::selection {
  background-color: #184E45;
  color: #81B5AC
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #2B685E -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #2B685E
}
/* base04 - #5F9C92 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #5F9C92
}
/* base05 - #81B5AC -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #81B5AC
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #3E9688
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #3E7996
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #3E4C96
}
.hljs-strong {
  font-weight: bold;
  color: #3E4C96
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #883E96
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #963E4C
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #96883E
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #4C963E
}
.hljs-emphasis {
  color: #4C963E;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #3E965B
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}