pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Atelier Estuary Light
  Author: <PERSON> (http://atelierbramdehaan.nl)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme atelier-estuary-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f4f3ec  Default Background
base01  #e7e6df  Lighter Background (Used for status bars, line number and folding marks)
base02  #929181  Selection Background
base03  #878573  Comments, Invisibles, Line Highlighting
base04  #6c6b5a  Dark Foreground (Used for status bars)
base05  #5f5e4e  Default Foreground, Caret, Delimiters, Operators
base06  #302f27  Light Foreground (Not often used)
base07  #22221b  Light Background (Not often used)
base08  #ba6236  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ae7313  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #a5980d  Classes, Markup Bold, Search Text Background
base0B  #7d9726  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #5b9d48  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #36a166  Functions, Methods, Attribute IDs, Headings
base0E  #5f9182  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #9d6c7c  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #5f5e4e;
  background: #f4f3ec
}
.hljs::selection,
.hljs ::selection {
  background-color: #929181;
  color: #5f5e4e
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #878573 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #878573
}
/* base04 - #6c6b5a -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #6c6b5a
}
/* base05 - #5f5e4e -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #5f5e4e
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ba6236
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ae7313
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #a5980d
}
.hljs-strong {
  font-weight: bold;
  color: #a5980d
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7d9726
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #5b9d48
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #36a166
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #5f9182
}
.hljs-emphasis {
  color: #5f9182;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #9d6c7c
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}