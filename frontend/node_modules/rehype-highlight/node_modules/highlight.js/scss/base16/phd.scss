pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: PhD
  Author: <PERSON><PERSON><PERSON> (http://leetless.de/vim.html)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme phd
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #061229  Default Background
base01  #2a3448  Lighter Background (Used for status bars, line number and folding marks)
base02  #4d5666  Selection Background
base03  #717885  Comments, Invisibles, Line Highlighting
base04  #9a99a3  Dark Foreground (Used for status bars)
base05  #b8bbc2  Default Foreground, Caret, Delimiters, Operators
base06  #dbdde0  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #d07346  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #f0a000  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fbd461  Classes, Markup Bold, Search Text Background
base0B  #99bf52  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #72b9bf  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #5299bf  Functions, Methods, Attribute IDs, Headings
base0E  #9989cc  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b08060  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #b8bbc2;
  background: #061229
}
.hljs::selection,
.hljs ::selection {
  background-color: #4d5666;
  color: #b8bbc2
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #717885 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #717885
}
/* base04 - #9a99a3 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #9a99a3
}
/* base05 - #b8bbc2 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #b8bbc2
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #d07346
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #f0a000
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fbd461
}
.hljs-strong {
  font-weight: bold;
  color: #fbd461
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #99bf52
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #72b9bf
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #5299bf
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #9989cc
}
.hljs-emphasis {
  color: #9989cc;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b08060
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}