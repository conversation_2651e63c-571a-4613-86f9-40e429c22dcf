pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Darktooth
  Author: <PERSON> (https://github.com/jasonm23)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme darktooth
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #1D2021  Default Background
base01  #32302F  Lighter Background (Used for status bars, line number and folding marks)
base02  #504945  Selection Background
base03  #665C54  Comments, Invisibles, Line Highlighting
base04  #928374  Dark Foreground (Used for status bars)
base05  #A89984  Default Foreground, Caret, Delimiters, Operators
base06  #D5C4A1  Light Foreground (Not often used)
base07  #FDF4C1  Light Background (Not often used)
base08  #FB543F  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #FE8625  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #FAC03B  Classes, Markup Bold, Search Text Background
base0B  #95C085  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #8BA59B  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #0D6678  Functions, Methods, Attribute IDs, Headings
base0E  #8F4673  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #A87322  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #A89984;
  background: #1D2021
}
.hljs::selection,
.hljs ::selection {
  background-color: #504945;
  color: #A89984
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #665C54 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #665C54
}
/* base04 - #928374 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #928374
}
/* base05 - #A89984 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #A89984
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #FB543F
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #FE8625
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #FAC03B
}
.hljs-strong {
  font-weight: bold;
  color: #FAC03B
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #95C085
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #8BA59B
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #0D6678
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #8F4673
}
.hljs-emphasis {
  color: #8F4673;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #A87322
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}