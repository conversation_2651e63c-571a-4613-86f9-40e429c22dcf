pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Tomorrow
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme tomorrow
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #ffffff  Default Background
base01  #e0e0e0  Lighter Background (Used for status bars, line number and folding marks)
base02  #d6d6d6  Selection Background
base03  #8e908c  Comments, Invisibles, Line Highlighting
base04  #969896  Dark Foreground (Used for status bars)
base05  #4d4d4c  Default Foreground, Caret, Delimiters, Operators
base06  #282a2e  Light Foreground (Not often used)
base07  #1d1f21  Light Background (Not often used)
base08  #c82829  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #f5871f  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #eab700  Classes, Markup Bold, Search Text Background
base0B  #718c00  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #3e999f  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #4271ae  Functions, Methods, Attribute IDs, Headings
base0E  #8959a8  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #a3685a  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #4d4d4c;
  background: #ffffff
}
.hljs::selection,
.hljs ::selection {
  background-color: #d6d6d6;
  color: #4d4d4c
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #8e908c -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #8e908c
}
/* base04 - #969896 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #969896
}
/* base05 - #4d4d4c -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #4d4d4c
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #c82829
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #f5871f
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #eab700
}
.hljs-strong {
  font-weight: bold;
  color: #eab700
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #718c00
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #3e999f
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #4271ae
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #8959a8
}
.hljs-emphasis {
  color: #8959a8;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #a3685a
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}