# Supported Languages

The table below shows the full list of languages (and corresponding classes/aliases) supported by Highlight.js.  Languages that list a **Package** below are 3rd party languages and are not bundled with the core library.  You can find their repositories by following the links.

**Note:** The languages available will depend on how you've built or are included the library in your app. For example our default minified web build includes only ~40 popular languages.  See [Getting the Library][1] and [Importing the Library][2] in the README for examples of how to load additional/specific languages.

<!-- LANGLIST -->
| Language                | Aliases                | Package |
| :-----------------------| :--------------------- | :------ |
| 1C                      | 1c                     |         |
| 4D                      | 4d                     |[highlightjs-4d](https://github.com/highlightjs/highlightjs-4d) |
| ABAP                    | sap-abap, abap         |[highlight-sap-abap](https://github.com/highlightjs/highlightjs-sap-abap) |
| ABNF                    | abnf                   |         |
| Access logs             | accesslog              |         |
| Ada                     | ada                    |         |
| Apex                    | apex                   | [highlightjs-apex](https://github.com/highlightjs/highlightjs-apex/)   |
| Arduino (C++ w/Arduino libs) | arduino, ino           |         |
| ARM assembler           | armasm, arm            |         |
| AVR assembler           | avrasm                 |         |
| ActionScript            | actionscript, as       |         |
| Alan IF                 | alan, i                | [highlightjs-alan](https://github.com/highlightjs/highlightjs-alan) |
| Alan                    | ln                     | [highlightjs-alan](https://github.com/alantech/highlightjs-alan) |
| AngelScript             | angelscript, asc       |         |
| Apache                  | apache, apacheconf     |         |
| AppleScript             | applescript, osascript |         |
| Arcade                  | arcade                 |         |
| AsciiDoc                | asciidoc, adoc         |         |
| AspectJ                 | aspectj                |         |
| AutoHotkey              | autohotkey             |         |
| AutoIt                  | autoit                 |         |
| Awk                     | awk, mawk, nawk, gawk  |         |
| Ballerina               | ballerina, bal         | [highlightjs-ballerina](https://github.com/highlightjs/highlightjs-ballerina) |
| Bash                    | bash, sh, zsh          |         |
| Basic                   | basic                  |         |
| BBCode                  | bbcode                 | [highlightjs-bbcode](https://github.com/RedGuy12/highlightjs-bbcode) |
| Blade (Laravel)         | blade                  | [highlightjs-blade](https://github.com/miken32/highlightjs-blade) |
| BNF                     | bnf                    |         |
| BQN                     | bqn                    | [highlightjs-bqn](https://github.com/razetime/highlightjs-bqn) |
| Brainfuck               | brainfuck, bf          |         |
| C#                      | csharp, cs             |         |
| C                       | c, h                   |         |
| C++                     | cpp, hpp, cc, hh, c++, h++, cxx, hxx |   |
| C/AL                    | cal                    |         |
| C3                      | c3                     | [highlightjs-c3](https://github.com/highlightjs/highlightjs-c3) |
| Cache Object Script     | cos, cls               |         |
| Candid                  | candid, did            | [highlightjs-motoko](https://github.com/rvanasa/highlightjs-motoko) |
| CMake                   | cmake, cmake.in        |         |
| COBOL                   | cobol, standard-cobol  | [highlightjs-cobol](https://github.com/otterkit/highlightjs-cobol) |
| CODEOWNERS              | codeowners             | [highlightjs-codeowners](https://github.com/highlightjs/highlightjs-codeowners) |
| Coq                     | coq                    |         |
| CSP                     | csp                    |         |
| CSS                     | css                    |         |
| Cap’n Proto             | capnproto, capnp       |         |
| Chaos                   | chaos, kaos            | [highlightjs-chaos](https://github.com/chaos-lang/highlightjs-chaos) |
| Chapel                  | chapel, chpl           | [highlightjs-chapel](https://github.com/chapel-lang/highlightjs-chapel) |
| Cisco CLI               | cisco                  | [highlightjs-cisco-cli](https://github.com/BMatheas/highlightjs-cisco-cli) |
| Clojure                 | clojure, clj           |         |
| CoffeeScript            | coffeescript, coffee, cson, iced | |
| CpcdosC+                | cpc                    | [highlightjs-cpcdos](https://github.com/SPinti-Software/highlightjs-cpcdos) |
| Crmsh                   | crmsh, crm, pcmk       |         |
| Crystal                 | crystal, cr            |         |
| cURL                    | curl                   | [highlightjs-curl](https://github.com/highlightjs/highlightjs-curl) |
| Cypher (Neo4j)          | cypher                 | [highlightjs-cypher](https://github.com/highlightjs/highlightjs-cypher) |
| D                       | d                      |         |
| Dafny                   | dafny                  | [highlightjs-dafny](https://github.com/ConsenSys/highlightjs-dafny)|
| Dart                    | dart                   |         |
| Delphi                  | dpr, dfm, pas, pascal  |         |
| Diff                    | diff, patch            |         |
| Django                  | django, jinja          |         |
| DNS Zone file           | dns, zone, bind        |         |
| Dockerfile              | dockerfile, docker     |         |
| DOS                     | dos, bat, cmd          |         |
| dsconfig                | dsconfig               |         |
| DTS (Device Tree)       | dts                    |         |
| Dust                    | dust, dst              |         |
| Dylan                   | dylan                  | [highlightjs-dylan](https://github.com/highlightjs/highlightjs-dylan) |
| EBNF                    | ebnf                   |         |
| Elixir                  | elixir                 |         |
| Elm                     | elm                    |         |
| Erlang                  | erlang, erl            |         |
| Excel                   | excel, xls, xlsx       |         |
| Extempore               | extempore, xtlang, xtm | [highlightjs-xtlang](https://github.com/highlightjs/highlightjs-xtlang) |
| F#                      | fsharp, fs, fsx, fsi, fsscript   |         |
| FIX                     | fix                    |         |
| Flix                    | flix                   | [highlightjs-flix](https://github.com/flix/highlightjs-flix) |
| Fortran                 | fortran, f90, f95      |         |
| FunC                    | func                   | [highlightjs-func](https://github.com/highlightjs/highlightjs-func) |
| G-Code                  | gcode, nc              |         |
| Gams                    | gams, gms              |         |
| GAUSS                   | gauss, gss             |         |
| GDScript                | godot, gdscript        | [highlightjs-gdscript](https://github.com/highlightjs/highlightjs-gdscript) |
| Gherkin                 | gherkin                |         |
| Glimmer and EmberJS     | hbs, glimmer, html.hbs, html.handlebars, htmlbars | [highlightjs-glimmer](https://github.com/NullVoxPopuli/highlightjs-glimmer) |
| GN for Ninja            | gn, gni                | [highlightjs-GN](https://github.com/highlightjs/highlightjs-GN) |
| Go                      | go, golang             |         |
| Grammatical Framework   | gf                     | [highlightjs-gf](https://github.com/johnjcamilleri/highlightjs-gf) |
| Golo                    | golo, gololang         |         |
| Gradle                  | gradle                 |         |
| GraphQL                 | graphql, gql           |         |
| Groovy                  | groovy                 |         |
| GSQL                    | gsql                   | [highlightjs-gsql](https://github.com/DanBarkus/highlightjs-gsql) |
| HTML, XML               | xml, html, xhtml, rss, atom, xjb, xsd, xsl, plist, svg | |
| HTTP                    | http, https            |         |
| Haml                    | haml                   |         |
| Handlebars              | handlebars, hbs, html.hbs, html.handlebars        | |
| Haskell                 | haskell, hs            |         |
| Haxe                    | haxe, hx               |         |
| High-level shader language| hlsl                | [highlightjs-hlsl](https://github.com/highlightjs/highlightjs-hlsl) |
| Hy                      | hy, hylang             |         |
| Ini, TOML               | ini, toml              |         |
| Inform7                 | inform7, i7            |         |
| IRPF90                  | irpf90                 |         |
| Iptables                | iptables               | [highlightjs-iptables](https://github.com/highlightjs/highlightjs-iptables) |
| JSON                    | json, jsonc            |         |
| JSONata                 | jsonata                | [highlightjs-jsonata](https://github.com/DevDimov/highlightjs-jsonata) |
| Java                    | java, jsp              |         |
| JavaScript              | javascript, js, jsx    |         |
| Jolie                   | jolie, iol, ol         | [highlightjs-jolie](https://github.com/xiroV/highlightjs-jolie) |
| Julia                   | julia, jl               |         |
| Julia REPL              | julia-repl             |         |
| Kotlin                  | kotlin, kt             |         |
| Lang                    |                        | [highlightjs-lang](https://github.com/highlightjs/highlightjs-lang)
| LaTeX                   | tex                    |         |
| Leaf                    | leaf                   |         |
| Lean                    | lean                   | [highlightjs-lean](https://github.com/leanprover-community/highlightjs-lean) |
| Lasso                   | lasso, ls, lassoscript |         |
| Less                    | less                   |         |
| LDIF                    | ldif                   |         |
| Liquid                  | liquid                 | [highlightjs-liquid](https://github.com/highlightjs/highlightjs-liquid) |
| Lisp                    | lisp                   |         |
| LiveCode Server         | livecodeserver         |         |
| LiveScript              | livescript, ls         |         |
| LookML                  | lookml                 | [highlightjs-lookml](https://github.com/spectacles-ci/highlightjs-lookml) |
| Lua                     | lua, pluto             |         |
| Luau                    | luau                   | [highlightjs-luau](https://github.com/highlightjs/highlightjs-luau) |
| Macaulay2               | macaulay2              | [highlightjs-macaulay2](https://github.com/d-torrance/highlightjs-macaulay2) |
| Makefile                | makefile, mk, mak, make |        |
| Markdown                | markdown, md, mkdown, mkd |      |
| Mathematica             | mathematica, mma, wl   |         |
| Matlab                  | matlab                 |         |
| Maxima                  | maxima                 |         |
| Maya Embedded Language  | mel                    |         |
| Mercury                 | mercury                |         |
| MetaPost                | metapost               | [highlightjs-metapost](https://github.com/chupinmaxime/highlightjs-metapost) |        |
| MIPS Assembler          | mips, mipsasm          |         |
| Mint                    | mint                   | [highlightjs-mint](https://github.com/mint-lang/highlightjs-mint) |
| Mirth                   | mirth                  | [highlightjs-mirth](https://github.com/highlightjs/highlightjs-mirth) |
| mIRC Scripting Language | mirc, mrc              | [highlightjs-mirc](https://github.com/highlightjs/highlightjs-mirc) |
| Mizar                   | mizar                  |         |
| MKB                     | mkb                    | [highlightjs-mkb](https://github.com/Dereavy/highlightjs-mkb) |
| MLIR                    | mlir                   | [highlightjs-mlir](https://github.com/highlightjs/highlightjs-mlir) |
| Mojolicious             | mojolicious            |         |
| Monkey                  | monkey                 |         |
| Moonscript              | moonscript, moon       |         |
| Motoko                  | motoko, mo             | [highlightjs-motoko](https://github.com/rvanasa/highlightjs-motoko) |
| N1QL                    | n1ql                   |         |
| NSIS                    | nsis                   |         |
| Never                   | never                  | [highlightjs-never](https://github.com/never-lang/highlightjs-never) |
| Nginx                   | nginx, nginxconf       |         |
| Nim                     | nim, nimrod            |         |
| Nix                     | nix                    |         |
| Oak                     | oak                    | [highlightjs-oak](https://github.com/timlabs/highlightjs-oak) |
| Object Constraint Language | ocl                 | [highlightjs-ocl](https://github.com/nhomble/highlightjs-ocl)        |
| OCaml                   | ocaml, ml              |         |
| Objective C             | objectivec, mm, objc, obj-c, obj-c++, objective-c++ |    |
| Odin                    | odin                   |         [highlightjs-odin](https://github.com/NinjasCL/highlightjs-odin) |
| OpenGL Shading Language | glsl                   |         |
| OpenSCAD                | openscad, scad         |         |
| Oracle Rules Language   | ruleslanguage          |         |
| Oxygene                 | oxygene                |         |
| PF                      | pf, pf.conf            |         |
| PHP                     | php                    |         |
| Papyrus                 | papyrus, psc           |[highlightjs-papyrus](https://github.com/Pickysaurus/highlightjs-papyrus)    |
| Parser3                 | parser3                |         |
| Perl                    | perl, pl, pm           |         |
| Phix                    | phix                   | [highlightjs-phix](https://github.com/highlightjs/highlightjs-phix) |
| Pine Script             | pine, pinescript       | [highlightjs-pine](https://github.com/jeyllani/highlightjs-pine) |
| Plaintext               | plaintext, txt, text   |         |
| Pony                    | pony                   |         |
| PostgreSQL & PL/pgSQL   | pgsql, postgres, postgresql |    |
| PowerOn                 | poweron, po            | [highlightjs-poweron](https://github.com/libum-llc/highlightjs-poweron) |
| PowerShell              | powershell, ps, ps1    |         |
| Processing              | processing             |         |
| Prolog                  | prolog                 |         |
| Properties              | properties             |         |
| Protocol Buffers        | proto, protobuf        |         |
| Puppet                  | puppet, pp             |         |
| Python                  | python, py, gyp        |         |
| Python profiler results | profile                |         |
| Python REPL             | python-repl, pycon     |         |
| Q#                      | qsharp                 | [highlightjs-qsharp](https://github.com/fedonman/highlightjs-qsharp) |
| Q                       | k, kdb                 |         |
| QML                     | qml                    |         |
| R                       | r                      |         |
| Raku                    | raku, perl6, p6, pm6, rakumod | [highlightjs-raku](https://github.com/highlightjs/highlightjs-raku) |
| RakuDoc                 | pod6, rakudoc          | [highlightjs-raku](https://github.com/highlightjs/highlightjs-raku) |
| RakuQuoting             | rakuquoting            | [highlightjs-raku](https://github.com/highlightjs/highlightjs-raku) |
| RakuRegexe              | rakuregexe                           | [highlightjs-raku](https://github.com/highlightjs/highlightjs-raku) |
| Razor CSHTML            | cshtml, razor, razor-cshtml | [highlightjs-cshtml-razor](https://github.com/highlightjs/highlightjs-cshtml-razor) |
| ReasonML                | reasonml, re           |         |
| Rebol & Red             | redbol, rebol, red, red-system | [highlightjs-redbol](https://github.com/oldes/highlightjs-redbol) |
| RenderMan RIB           | rib                    |         |
| RenderMan RSL           | rsl                    |         |
| ReScript                | rescript, res          | [highlightjs-rescript](https://github.com/tsnobip/highlightjs-rescript) |
| RiScript                | risc, riscript         | [highlightjs-riscript](https://github.com/highlightjs/highlightjs-riscript) |
| RISC-V Assembly         | riscv, riscvasm        | [highlightjs-riscvasm](https://github.com/highlightjs/highlightjs-riscvasm) |
| Roboconf                | graph, instances       |         |
| Robot Framework         | robot, rf              | [highlightjs-robot](https://github.com/highlightjs/highlightjs-robot) |
| RPM spec files          | rpm-specfile, rpm, spec, rpm-spec, specfile | [highlightjs-rpm-specfile](https://github.com/highlightjs/highlightjs-rpm-specfile) |
| Ruby                    | ruby, rb, gemspec, podspec, thor, irb | |
| Rust                    | rust, rs               |         |
| RVT Script              | rvt, rvt-script        | [highlightjs-rvt-script](https://github.com/Sopitive/highlightjs-rvt-script) |
| SAS                     | SAS, sas               |         |
| SCSS                    | scss                   |         |
| SQL                     | sql                    |         |
| STEP Part 21            | p21, step, stp         |         |
| Scala                   | scala                  |         |
| Scheme                  | scheme                 |         |
| Scilab                  | scilab, sci            |         |
| SFZ                     | sfz                    | [highlightjs-sfz](https://github.com/sfz/highlight.js) |
| Shape Expressions       | shexc                  | [highlightjs-shexc](https://github.com/highlightjs/highlightjs-shexc) |
| Shell                   | shell, console         |         |
| Smali                   | smali                  |         |
| Smalltalk               | smalltalk, st          |         |
| SML                     | sml, ml                |         |
| Solidity                | solidity, sol          | [highlightjs-solidity](https://github.com/highlightjs/highlightjs-solidity) |
| Splunk SPL              | spl                    | [highlightjs-spl](https://github.com/swsoyee/highlightjs-spl) |
| Stan                    | stan, stanfuncs        |         |
| Stata                   | stata                  |         |
| Structured Text         | iecst, scl, stl, structured-text | [highlightjs-structured-text](https://github.com/highlightjs/highlightjs-structured-text) |
| Stylus                  | stylus, styl           |         |
| SubUnit                 | subunit                |         |
| Supercollider           | supercollider, sc      | [highlightjs-supercollider](https://github.com/highlightjs/highlightjs-supercollider) |
| Svelte                  | svelte                 | [highlight.svelte](https://github.com/moonlitgrace/highlight.svelte) |
| Swift                   | swift                  |         |
| Tcl                     | tcl, tk                |         |
| Terraform (HCL)         | terraform, tf, hcl     | [highlightjs-terraform](https://github.com/highlightjs/highlightjs-terraform) |
| Test Anything Protocol  | tap                    |         |
| Thrift                  | thrift                 |         |
| Toit                    | toit                   | [toit-highlight](https://github.com/snxx-lppxx/toit-highlight) |
| TP                      | tp                     |         |
| Transact-SQL            | tsql                   | [highlightjs-tsql](https://github.com/highlightjs/highlightjs-tsql) |
| TTCN-3                  | ttcn, ttcnpp, ttcn3    | [highlightjs-ttcn3](https://gitea.osmocom.org/ttcn3/highlightjs-ttcn3) |
| Twig                    | twig, craftcms         |         |
| TypeScript              | typescript, ts, tsx, mts, cts |         |
| Unicorn Rails log       | unicorn-rails-log      | [highlightjs-unicorn-rails-log](https://github.com/sweetppro/highlightjs-unicorn-rails-log) |
| Unison                  | unison, u              | [highlightjs-unison](https://github.com/highlightjs/highlightjs-unison) |
| VB.Net                  | vbnet, vb              |         |
| VBA                     | vba                    | [highlightjs-vba](https://github.com/dullin/highlightjs-vba) |
| VBScript                | vbscript, vbs          |         |
| VHDL                    | vhdl                   |         |
| Vala                    | vala                   |         |
| Verilog                 | verilog, v             |         |
| Vim Script              | vim                    |         |
| WGSL                    | wgsl                   | [highlightjs-wgsl](https://github.com/highlightjs/highlightjs-wgsl) |
| X#                      | xsharp, xs, prg        | [highlightjs-xsharp](https://github.com/InfomindsAg/highlightjs-xsharp) |
| X++                     | axapta, x++            |         |
| x86 Assembly            | x86asm                 |         |
| x86 Assembly (AT&T)     | x86asmatt              | [highlightjs-x86asmatt](https://github.com/gondow/highlightjs-x86asmatt)  |
| XL                      | xl, tao                |         |
| XQuery                  | xquery, xpath, xq, xqm |         |
| YAML                    | yml, yaml              |         |
| ZenScript               | zenscript, zs          |[highlightjs-zenscript](https://github.com/highlightjs/highlightjs-zenscript) |
| Zephir                  | zephir, zep            |         |
| Zig                     | zig                    |[highlightjs-zig](https://github.com/fwx5618177/highlightjs-zig) |
<!-- LANGLIST_END -->

<!-- document it until we can fix it -->
## Alias Overlap

If you are using either of these languages at the same time please be sure to
use the full name and not the alias to avoid any ambiguity.

| Language                | Overlap                |
| :-----------------------| :--------------------- |
| SML                     | ml                     |
| OCaml                   | ml                     |
| Lasso                   | ls                     |
| LiveScript              | ls                     |


[1]: https://github.com/highlightjs/highlight.js#getting-the-library
[2]: https://github.com/highlightjs/highlight.js#importing-the-library
