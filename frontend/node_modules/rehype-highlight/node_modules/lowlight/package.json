{"name": "lowlight", "version": "3.3.0", "description": "Virtual syntax highlighting for virtual DOMs and non-HTML things", "license": "MIT", "keywords": ["syntax", "code", "ast", "virtual", "dom", "highlight", "highlighting"], "repository": "wooorm/lowlight", "bugs": "https://github.com/wooorm/lowlight/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON>i <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/hast": "^3.0.0", "devlop": "^1.0.0", "highlight.js": "~11.11.0"}, "devDependencies": {"@types/mdast": "^4.0.0", "@types/node": "^22.0.0", "@types/unist": "^3.0.0", "c8": "^10.0.0", "chalk": "^5.0.0", "estree-util-is-identifier-name": "^3.0.0", "hast-util-to-html": "^9.0.0", "mdast-zone": "^6.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "unified": "^11.0.0", "xo": "^0.60.0"}, "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . --frail --output --quiet && prettier . --log-level warn --write && xo --fix", "generate": "node --conditions development script/build-registry.js", "prepack": "npm run generate && npm run build && npm run format", "test": "npm run generate && npm run build && npm run format && npm run test-coverage", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --check-coverage --reporter lcov npm run test-api"}, "prettier": {"bracketSpacing": false, "singleQuote": true, "semi": false, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm", "./script/support.js"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/consistent-type-definitions": "off"}}, {"files": ["script/**/*.js", "test/**/*.js"], "rules": {"no-await-in-loop": "off"}}], "prettier": true, "rules": {"n/file-extension-in-import": "off", "unicorn/no-this-assignment": "off", "unicorn/prefer-at": "off", "unicorn/prefer-type-error": "off"}}}