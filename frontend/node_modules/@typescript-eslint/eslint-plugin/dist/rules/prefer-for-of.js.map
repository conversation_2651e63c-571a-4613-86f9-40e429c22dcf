{"version": 3, "file": "prefer-for-of.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-for-of.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAG+C;AAE/C,kCAAqC;AAErC,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,8EAA8E;YAChF,WAAW,EAAE,WAAW;SACzB;QACD,QAAQ,EAAE;YACR,WAAW,EACT,8EAA8E;SACjF;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,SAAS,2BAA2B,CAClC,IAA0B;YAE1B,OAAO,CACL,IAAI,EAAE,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBACjD,IAAI,CAAC,IAAI,KAAK,OAAO;gBACrB,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,CAC/B,CAAC;QACJ,CAAC;QAED,SAAS,SAAS,CAChB,IAAsD,EACtD,KAAa;YAEb,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC;QACtE,CAAC;QAED,SAAS,iBAAiB,CAAC,IAAiC;YAC1D,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,SAAS,oBAAoB,CAC3B,IAAsD,EACtD,IAAY;YAEZ,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;QACvE,CAAC;QAED,SAAS,0BAA0B,CACjC,IAA0B,EAC1B,IAAY;YAEZ,IACE,IAAI,EAAE,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC9C,IAAI,CAAC,QAAQ,KAAK,GAAG;gBACrB,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBACnD,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EACnD,CAAC;gBACD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAC3B,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,WAAW,CAAC,IAA0B,EAAE,IAAY;YAC3D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,OAAO,KAAK,CAAC;YACf,CAAC;YACD,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,sBAAc,CAAC,gBAAgB;oBAClC,aAAa;oBACb,OAAO,CACL,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CACpE,CAAC;gBACJ,KAAK,sBAAc,CAAC,oBAAoB;oBACtC,IAAI,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;wBAC1C,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;4BAC3B,SAAS;4BACT,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;wBAClC,CAAC;6BAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;4BACjC,yBAAyB;4BACzB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;4BACxB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gCAC7C,IAAI,CAAC,QAAQ,KAAK,GAAG;gCACrB,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;oCACrC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;oCACzB,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;wCACtB,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAC7C,CAAC;wBACJ,CAAC;oBACH,CAAC;YACL,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,QAAQ,CAAC,KAAoB,EAAE,KAAoB;YAC1D,OAAO,CACL,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACrE,CAAC;QACJ,CAAC;QAED,SAAS,UAAU,CAAC,IAAmB;YACrC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;YAED,4BAA4B;YAC5B,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB;gBACnD,MAAM,CAAC,IAAI,KAAK,IAAI,EACpB,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,cAAc;YACd,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC9C,MAAM,CAAC,QAAQ,KAAK,QAAQ;gBAC5B,MAAM,CAAC,QAAQ,KAAK,IAAI,EACxB,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,uBAAuB;YACvB,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC/C,MAAM,CAAC,QAAQ,KAAK,IAAI,EACxB,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,eAAe;YACf,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE,CAAC;gBAChD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,kBAAkB;YAClB,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE,CAAC;gBAC/C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,+BAA+B;YAC/B,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;gBACvC,MAAM,CAAC,KAAK,KAAK,IAAI;gBACrB,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBACtD,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EACzB,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,wBAAwB,CAC/B,IAAwB,EACxB,QAAiC,EACjC,eAAoC;YAEpC,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;YAC1C,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACtD,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;gBAC3C,MAAM,EAAE,GAAG,SAAS,CAAC,UAAU,CAAC;gBAChC,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC;gBACvB,OAAO,CACL,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;oBACnB,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;wBAClD,IAAI,CAAC,QAAQ,KAAK,EAAE;wBACpB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,SAAS;wBAC7C,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CACrB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,mBAAmB,CAAC,IAA2B;gBAC7C,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5C,OAAO;gBACT,CAAC;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAE9B,CAAC;gBACd,IACE,CAAC,UAAU;oBACX,CAAC,iBAAiB,CAAC,UAAU,CAAC;oBAC9B,UAAU,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAChD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC;gBACrC,MAAM,eAAe,GAAG,0BAA0B,CAChD,IAAI,CAAC,IAAI,EACT,SAAS,CACV,CAAC;gBACF,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,OAAO;gBACT,CAAC;gBAED,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAA,mCAAoB,EAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5D,IACE,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;oBACnC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,eAAe,CAAC,EAC9D,CAAC;oBACD,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,aAAa;qBACzB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}