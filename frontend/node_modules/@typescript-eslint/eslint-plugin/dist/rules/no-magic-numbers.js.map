{"version": 3, "file": "no-magic-numbers.js", "sourceRoot": "", "sources": ["../../src/rules/no-magic-numbers.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAO1D,kCAAgD;AAChD,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,kBAAkB,CAAC,CAAC;AAKvD,iFAAiF;AACjF,MAAM,MAAM,GAAG,IAAA,gBAAS;AACtB,yHAAyH;AACzH,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IACjC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EACxB;IACE,UAAU,EAAE;QACV,yBAAyB,EAAE;YACzB,IAAI,EAAE,SAAS;SAChB;QACD,WAAW,EAAE;YACX,IAAI,EAAE,SAAS;SAChB;QACD,6BAA6B,EAAE;YAC7B,IAAI,EAAE,SAAS;SAChB;QACD,iBAAiB,EAAE;YACjB,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CACwB,CAAC;AAE5B,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,wBAAwB;YACrC,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE,CAAC,MAAM,CAAC;QAChB,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE;QACd;YACE,MAAM,EAAE,EAAE;YACV,kBAAkB,EAAE,KAAK;YACzB,YAAY,EAAE,KAAK;YACnB,aAAa,EAAE,KAAK;YACpB,yBAAyB,EAAE,KAAK;YAChC,WAAW,EAAE,KAAK;YAClB,6BAA6B,EAAE,KAAK;SACrC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,OAAO;YACL,OAAO,CAAC,IAAI;gBACV,qDAAqD;gBACrD,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;oBACrE,OAAO;gBACT,CAAC;gBAED,wEAAwE;gBACxE,wEAAwE;gBACxE,wEAAwE;gBACxE,wEAAwE;gBACxE,IAAI,SAA8B,CAAC;gBAEnC,qDAAqD;gBACrD,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpC,SAAS,GAAG,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC;gBAC3C,CAAC;gBACD,sDAAsD;qBACjD,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;oBACtC,SAAS,GAAG,OAAO,CAAC,yBAAyB,KAAK,IAAI,CAAC;gBACzD,CAAC;gBACD,oCAAoC;qBAC/B,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC7C,SAAS,GAAG,OAAO,CAAC,iBAAiB,KAAK,IAAI,CAAC;gBACjD,CAAC;gBACD,iDAAiD;qBAC5C,IAAI,oCAAoC,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpD,SAAS,GAAG,OAAO,CAAC,6BAA6B,KAAK,IAAI,CAAC;gBAC7D,CAAC;gBAED,wEAAwE;gBACxE,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;oBACvB,OAAO;gBACT,CAAC;gBACD,yDAAyD;qBACpD,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;oBAC7B,IAAI,cAAc,GAChB,IAAI,CAAC;oBACP,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;oBAEnB,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBACnD,6DAA6D;wBAC7D,oHAAoH;wBACpH,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,EAC5B,CAAC;wBACD,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;wBAC7B,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBAC7C,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,SAAS;wBACpB,IAAI,EAAE,cAAc;wBACpB,IAAI,EAAE,EAAE,GAAG,EAAE;qBACd,CAAC,CAAC;oBAEH,OAAO;gBACT,CAAC;gBAED,uCAAuC;gBACvC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH;;GAEG;AACH,SAAS,gBAAgB,CAAC,IAAsB;IAC9C,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;QACnD,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EACzC,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED,OAAO,IAAI,CAAC,MAAM,CAAC;AACrB,CAAC;AAED;;;;;GAKG;AACH,SAAS,mCAAmC,CAAC,IAAmB;IAC9D,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,sBAAsB,CAAC;AAC7E,CAAC;AAED;;;;;GAKG;AACH,SAAS,wBAAwB,CAAC,IAAmB;IACnD,IAAI,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE,CAAC;QAC7D,OAAO,mCAAmC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,SAAS,yBAAyB,CAAC,IAAsB;IACvD,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACtC,OAAO,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,YAAY,CAAC;AACtD,CAAC;AAED;;;;;GAKG;AACH,SAAS,qBAAqB,CAAC,IAAmB;IAChD,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC;AAC5D,CAAC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,IAAmB;IACjD,4CAA4C;IAC5C,IACE,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,eAAe;QACpD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,EAC5B,CAAC;QACD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,0DAA0D;IAC1D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,yDAAyD;IACzD,IAAI,mCAAmC,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0FAA0F;IAC1F,IAAI,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,SAAS,oCAAoC,CAAC,IAAsB;IAClE,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAEtC,IAAI,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,kBAAkB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC1E,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;GAKG;AACH,SAAS,6BAA6B,CAAC,IAAsB;IAC3D,oCAAoC;IACpC,IAAI,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAEtC,sEAAsE;IACtE,2BAA2B;IAC3B,OACE,QAAQ,EAAE,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,WAAW;QACrD,QAAQ,EAAE,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC5D,CAAC;QACD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC7B,CAAC;IAED,OAAO,QAAQ,EAAE,MAAM,EAAE,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CAAC;AACvE,CAAC"}