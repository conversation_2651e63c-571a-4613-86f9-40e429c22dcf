import React, { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [backendStatus, setBackendStatus] = useState<string>('检查中...')
  const [systemInfo, setSystemInfo] = useState<any>(null)

  useEffect(() => {
    // 检查后端连接状态
    const checkBackend = async () => {
      try {
        console.log('正在检查后端连接...')
        const response = await fetch('http://localhost:8000/health', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
        console.log('后端响应状态:', response.status)

        if (response.ok) {
          const data = await response.json()
          console.log('后端响应数据:', data)
          setBackendStatus('✅ 后端连接正常')
          setSystemInfo(data)
        } else {
          console.error('后端响应错误:', response.status, response.statusText)
          setBackendStatus(`❌ 后端连接失败 (${response.status})`)
        }
      } catch (error) {
        console.error('连接后端时发生错误:', error)
        setBackendStatus(`❌ 无法连接到后端: ${error.message}`)
      }
    }

    checkBackend()

    // 每30秒检查一次连接状态
    const interval = setInterval(checkBackend, 30000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 头部 */}
        <header className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            🚀 中小型企业智能应用系统
          </h1>
          <p className="text-xl text-gray-600">
            集成AI能力的企业级应用平台
          </p>
        </header>

        {/* 系统状态卡片 */}
        <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* 后端状态 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">
              🔧 后端服务状态
            </h3>
            <p className="text-sm text-gray-600 mb-2">
              状态: {backendStatus}
            </p>
            {systemInfo && (
              <div className="text-sm text-gray-500">
                <p>版本: {systemInfo.version}</p>
                <p>环境: {systemInfo.environment}</p>
              </div>
            )}
          </div>

          {/* 前端状态 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">
              🎨 前端应用状态
            </h3>
            <p className="text-sm text-gray-600 mb-2">
              状态: ✅ 前端运行正常
            </p>
            <div className="text-sm text-gray-500">
              <p>框架: React 18+ + TypeScript</p>
              <p>构建工具: Vite</p>
            </div>
          </div>
        </div>

        {/* 功能模块 */}
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            📋 系统功能模块
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* 用户管理 */}
            <div className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
              <div className="text-center">
                <div className="text-3xl mb-2">👥</div>
                <h4 className="font-semibold text-gray-800">用户管理</h4>
                <p className="text-sm text-gray-600 mt-2">
                  用户注册、登录、权限管理
                </p>
              </div>
            </div>

            {/* 文档处理 */}
            <div className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
              <div className="text-center">
                <div className="text-3xl mb-2">📄</div>
                <h4 className="font-semibold text-gray-800">文档处理</h4>
                <p className="text-sm text-gray-600 mt-2">
                  文档上传、解析、向量化
                </p>
              </div>
            </div>

            {/* 知识库 */}
            <div className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
              <div className="text-center">
                <div className="text-3xl mb-2">🧠</div>
                <h4 className="font-semibold text-gray-800">RAG知识库</h4>
                <p className="text-sm text-gray-600 mt-2">
                  智能检索、语义搜索
                </p>
              </div>
            </div>

            {/* AI模型 */}
            <div className="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
              <div className="text-center">
                <div className="text-3xl mb-2">🤖</div>
                <h4 className="font-semibold text-gray-800">AI模型</h4>
                <p className="text-sm text-gray-600 mt-2">
                  Ollama + 阿里云百炼
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* API 测试区域 */}
        <div className="max-w-4xl mx-auto mt-12">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            🔗 API 接口测试
          </h2>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => window.open('http://localhost:8000/docs', '_blank')}
                className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                📚 查看 API 文档
              </button>

              <button
                onClick={() => window.open('http://localhost:8000/health', '_blank')}
                className="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                ❤️ 健康检查
              </button>
            </div>
          </div>
        </div>

        {/* 底部信息 */}
        <footer className="text-center mt-12 text-gray-500">
          <p>🎯 面向中小型企业的智能应用开发架构</p>
          <p className="text-sm mt-2">
            技术栈: React + TypeScript + FastAPI + PostgreSQL + Redis + Ollama
          </p>
        </footer>
      </div>
    </div>
  )
}

export default App
