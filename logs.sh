#!/bin/bash

# 中小型企业智能应用系统 - 日志查看脚本
# 作者: ZHT开发团队

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${CYAN}用法: $0 [服务名] [选项]${NC}"
    echo ""
    echo "服务名:"
    echo "  frontend      查看前端日志"
    echo "  backend       查看后端日志"
    echo "  postgres      查看 PostgreSQL 日志"
    echo "  redis         查看 Redis 日志"
    echo "  ollama        查看 Ollama 日志"
    echo "  all           查看所有服务日志"
    echo ""
    echo "选项:"
    echo "  -f, --follow  实时跟踪日志"
    echo "  -n, --lines   显示最后 N 行日志 (默认: 50)"
    echo "  --clear       清空指定服务的日志"
    echo "  --help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 backend              # 查看后端最后50行日志"
    echo "  $0 frontend -f          # 实时跟踪前端日志"
    echo "  $0 backend -n 100       # 查看后端最后100行日志"
    echo "  $0 all                  # 查看所有服务日志"
    echo "  $0 frontend --clear     # 清空前端日志"
}

# 查看前端日志
show_frontend_logs() {
    local lines=${1:-50}
    local follow=${2:-false}
    
    echo -e "${CYAN}=== 前端服务日志 ===${NC}"
    
    if [ -f "logs/frontend.log" ]; then
        if [ "$follow" = true ]; then
            echo -e "${YELLOW}实时跟踪前端日志 (Ctrl+C 退出)...${NC}"
            tail -f logs/frontend.log
        else
            echo -e "${YELLOW}显示最后 $lines 行前端日志:${NC}"
            tail -n $lines logs/frontend.log
        fi
    else
        echo -e "${RED}前端日志文件不存在: logs/frontend.log${NC}"
        echo -e "${YELLOW}提示: 请确保前端服务已启动${NC}"
    fi
}

# 查看后端日志
show_backend_logs() {
    local lines=${1:-50}
    local follow=${2:-false}
    
    echo -e "${CYAN}=== 后端服务日志 ===${NC}"
    
    if command -v docker-compose &> /dev/null; then
        if [ "$follow" = true ]; then
            echo -e "${YELLOW}实时跟踪后端日志 (Ctrl+C 退出)...${NC}"
            docker-compose logs -f backend
        else
            echo -e "${YELLOW}显示最后 $lines 行后端日志:${NC}"
            docker-compose logs --tail=$lines backend
        fi
    else
        echo -e "${RED}Docker Compose 未安装，无法查看后端日志${NC}"
    fi
}

# 查看数据库日志
show_postgres_logs() {
    local lines=${1:-50}
    local follow=${2:-false}
    
    echo -e "${CYAN}=== PostgreSQL 日志 ===${NC}"
    
    if command -v docker-compose &> /dev/null; then
        if [ "$follow" = true ]; then
            echo -e "${YELLOW}实时跟踪 PostgreSQL 日志 (Ctrl+C 退出)...${NC}"
            docker-compose logs -f postgres
        else
            echo -e "${YELLOW}显示最后 $lines 行 PostgreSQL 日志:${NC}"
            docker-compose logs --tail=$lines postgres
        fi
    else
        echo -e "${RED}Docker Compose 未安装，无法查看 PostgreSQL 日志${NC}"
    fi
}

# 查看 Redis 日志
show_redis_logs() {
    local lines=${1:-50}
    local follow=${2:-false}
    
    echo -e "${CYAN}=== Redis 日志 ===${NC}"
    
    if command -v docker-compose &> /dev/null; then
        if [ "$follow" = true ]; then
            echo -e "${YELLOW}实时跟踪 Redis 日志 (Ctrl+C 退出)...${NC}"
            docker-compose logs -f redis
        else
            echo -e "${YELLOW}显示最后 $lines 行 Redis 日志:${NC}"
            docker-compose logs --tail=$lines redis
        fi
    else
        echo -e "${RED}Docker Compose 未安装，无法查看 Redis 日志${NC}"
    fi
}

# 查看 Ollama 日志
show_ollama_logs() {
    local lines=${1:-50}
    local follow=${2:-false}
    
    echo -e "${CYAN}=== Ollama 日志 ===${NC}"
    
    if command -v docker-compose &> /dev/null; then
        if [ "$follow" = true ]; then
            echo -e "${YELLOW}实时跟踪 Ollama 日志 (Ctrl+C 退出)...${NC}"
            docker-compose logs -f ollama
        else
            echo -e "${YELLOW}显示最后 $lines 行 Ollama 日志:${NC}"
            docker-compose logs --tail=$lines ollama
        fi
    else
        echo -e "${RED}Docker Compose 未安装，无法查看 Ollama 日志${NC}"
    fi
}

# 查看所有服务日志
show_all_logs() {
    local lines=${1:-50}
    local follow=${2:-false}
    
    echo -e "${CYAN}=== 所有服务日志 ===${NC}"
    
    if [ "$follow" = true ]; then
        echo -e "${YELLOW}实时跟踪所有服务日志 (Ctrl+C 退出)...${NC}"
        
        # 在后台启动前端日志跟踪
        if [ -f "logs/frontend.log" ]; then
            (echo -e "${BLUE}[前端]${NC}" && tail -f logs/frontend.log | sed 's/^/[前端] /') &
            local frontend_pid=$!
        fi
        
        # 启动后端服务日志跟踪
        if command -v docker-compose &> /dev/null; then
            docker-compose logs -f
        fi
        
        # 清理后台进程
        if [ -n "$frontend_pid" ]; then
            kill $frontend_pid 2>/dev/null || true
        fi
    else
        echo -e "${YELLOW}显示所有服务最后 $lines 行日志:${NC}"
        
        # 显示前端日志
        echo -e "\n${BLUE}=== 前端日志 ===${NC}"
        show_frontend_logs $lines false
        
        # 显示后端服务日志
        echo -e "\n${BLUE}=== 后端服务日志 ===${NC}"
        if command -v docker-compose &> /dev/null; then
            docker-compose logs --tail=$lines
        fi
    fi
}

# 清空日志
clear_logs() {
    local service=$1
    
    case $service in
        frontend)
            if [ -f "logs/frontend.log" ]; then
                > logs/frontend.log
                echo -e "${GREEN}前端日志已清空${NC}"
            else
                echo -e "${YELLOW}前端日志文件不存在${NC}"
            fi
            ;;
        backend|postgres|redis|ollama)
            echo -e "${YELLOW}Docker 容器日志无法直接清空${NC}"
            echo -e "${YELLOW}建议重启容器来清空日志: docker-compose restart $service${NC}"
            ;;
        all)
            # 清空前端日志
            if [ -f "logs/frontend.log" ]; then
                > logs/frontend.log
                echo -e "${GREEN}前端日志已清空${NC}"
            fi
            echo -e "${YELLOW}Docker 容器日志无法直接清空，建议重启服务${NC}"
            ;;
        *)
            echo -e "${RED}未知服务: $service${NC}"
            ;;
    esac
}

# 主函数
main() {
    local service=""
    local lines=50
    local follow=false
    local clear=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            frontend|backend|postgres|redis|ollama|all)
                service=$1
                shift
                ;;
            -f|--follow)
                follow=true
                shift
                ;;
            -n|--lines)
                lines=$2
                shift 2
                ;;
            --clear)
                clear=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}未知参数: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定服务，显示帮助
    if [ -z "$service" ]; then
        echo -e "${YELLOW}请指定要查看的服务${NC}"
        show_help
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 处理清空日志请求
    if [ "$clear" = true ]; then
        clear_logs $service
        exit 0
    fi
    
    # 显示日志
    case $service in
        frontend)
            show_frontend_logs $lines $follow
            ;;
        backend)
            show_backend_logs $lines $follow
            ;;
        postgres)
            show_postgres_logs $lines $follow
            ;;
        redis)
            show_redis_logs $lines $follow
            ;;
        ollama)
            show_ollama_logs $lines $follow
            ;;
        all)
            show_all_logs $lines $follow
            ;;
        *)
            echo -e "${RED}未知服务: $service${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
