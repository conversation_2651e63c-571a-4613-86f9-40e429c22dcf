#!/bin/bash

# 中小型企业智能应用系统 - 重启服务脚本
# 作者: ZHT开发团队

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}用法: $0 [服务名] [选项]${NC}"
    echo ""
    echo "服务名:"
    echo "  frontend      重启前端服务"
    echo "  backend       重启后端服务"
    echo "  postgres      重启 PostgreSQL 服务"
    echo "  redis         重启 Redis 服务"
    echo "  ollama        重启 Ollama 服务"
    echo "  all           重启所有服务 (默认)"
    echo ""
    echo "选项:"
    echo "  --quick       快速重启 (不重新构建)"
    echo "  --build       重新构建并重启"
    echo "  --clean       清理后重启"
    echo "  --help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 重启所有服务"
    echo "  $0 backend            # 只重启后端服务"
    echo "  $0 frontend --quick   # 快速重启前端"
    echo "  $0 all --build        # 重新构建并重启所有服务"
}

# 重启前端服务
restart_frontend() {
    local quick=${1:-false}
    
    log_step "重启前端服务..."
    
    # 停止前端服务
    if [ -f "logs/frontend.pid" ]; then
        local pid=$(cat logs/frontend.pid)
        if ps -p $pid > /dev/null 2>&1; then
            log_info "停止前端进程 (PID: $pid)..."
            kill $pid
            sleep 2
        fi
        rm -f logs/frontend.pid
    fi
    
    # 查找并停止所有前端进程
    pkill -f "vite" || true
    pkill -f "npm run dev" || true
    
    # 等待进程完全停止
    sleep 3
    
    # 启动前端服务
    cd frontend
    
    # 确保使用正确的 Node.js 版本
    if [ -f ~/.nvm/nvm.sh ]; then
        source ~/.nvm/nvm.sh
        nvm use --lts
    fi
    
    # 如果不是快速重启，重新安装依赖
    if [ "$quick" != true ]; then
        log_info "检查并更新前端依赖..."
        npm install
    fi
    
    # 启动前端开发服务器
    log_info "启动前端开发服务器..."
    nohup npm run dev > ../logs/frontend.log 2>&1 &
    echo $! > ../logs/frontend.pid
    
    cd ..
    
    # 等待前端启动
    log_info "等待前端服务启动..."
    sleep 10
    
    # 检查前端服务状态
    for i in {1..10}; do
        if curl -s http://localhost:5173 > /dev/null; then
            log_success "前端服务重启成功！"
            return 0
        else
            if [ $i -eq 10 ]; then
                log_error "前端服务重启失败"
                return 1
            fi
            log_info "等待前端服务启动... ($i/10)"
            sleep 3
        fi
    done
}

# 重启后端服务
restart_backend() {
    local build=${1:-false}
    
    log_step "重启后端服务..."
    
    if [ "$build" = true ]; then
        log_info "重新构建后端镜像..."
        docker-compose build backend
    fi
    
    log_info "重启后端容器..."
    docker-compose restart backend
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    sleep 15
    
    # 检查后端服务状态
    for i in {1..10}; do
        if curl -s http://localhost:8000/health > /dev/null; then
            log_success "后端服务重启成功！"
            return 0
        else
            if [ $i -eq 10 ]; then
                log_error "后端服务重启失败"
                docker-compose logs --tail=20 backend
                return 1
            fi
            log_info "等待后端服务启动... ($i/10)"
            sleep 3
        fi
    done
}

# 重启数据库服务
restart_database() {
    local service=$1
    
    log_step "重启 $service 服务..."
    
    docker-compose restart $service
    
    # 等待服务启动
    sleep 10
    
    log_success "$service 服务重启完成"
}

# 重启所有服务
restart_all() {
    local quick=${1:-false}
    local build=${2:-false}
    local clean=${3:-false}
    
    log_step "重启所有服务..."
    
    # 如果需要清理
    if [ "$clean" = true ]; then
        log_info "清理系统资源..."
        ./stop.sh --clean
        sleep 5
    else
        # 停止所有服务
        log_info "停止所有服务..."
        ./stop.sh
        sleep 5
    fi
    
    # 重新启动所有服务
    if [ "$build" = true ]; then
        log_info "重新构建并启动所有服务..."
        docker-compose build
        docker-compose up -d
    else
        log_info "启动所有服务..."
        docker-compose up -d
    fi
    
    # 等待后端服务启动
    log_info "等待后端服务启动..."
    sleep 20
    
    # 检查后端服务
    for i in {1..15}; do
        if curl -s http://localhost:8000/health > /dev/null; then
            log_success "后端服务启动成功！"
            break
        else
            if [ $i -eq 15 ]; then
                log_error "后端服务启动失败"
                docker-compose logs --tail=20 backend
                return 1
            fi
            log_info "等待后端服务启动... ($i/15)"
            sleep 3
        fi
    done
    
    # 启动前端服务
    restart_frontend $quick
    
    log_success "所有服务重启完成！"
}

# 主函数
main() {
    local service="all"
    local quick=false
    local build=false
    local clean=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            frontend|backend|postgres|redis|ollama|all)
                service=$1
                shift
                ;;
            --quick)
                quick=true
                shift
                ;;
            --build)
                build=true
                shift
                ;;
            --clean)
                clean=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}未知参数: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo -e "${CYAN}"
    echo "=================================================="
    echo "    中小型企业智能应用系统 - 服务重启脚本"
    echo "=================================================="
    echo -e "${NC}"
    
    # 创建日志目录
    mkdir -p logs
    
    # 根据服务类型执行重启
    case $service in
        frontend)
            restart_frontend $quick
            ;;
        backend)
            restart_backend $build
            ;;
        postgres|redis|ollama)
            restart_database $service
            ;;
        all)
            restart_all $quick $build $clean
            ;;
        *)
            log_error "未知服务: $service"
            show_help
            exit 1
            ;;
    esac
    
    # 显示服务状态
    echo -e "\n${CYAN}=== 服务状态检查 ===${NC}"
    ./status.sh
    
    echo -e "\n${GREEN}🔄 服务重启操作完成！${NC}"
    echo -e "${YELLOW}💡 提示: 使用 ./status.sh 查看详细状态${NC}"
    echo -e "${YELLOW}💡 提示: 使用 ./logs.sh [服务名] 查看日志${NC}"
}

# 执行主函数
main "$@"
